<?php
// 測試訊息邏輯
ini_set('display_errors', 'on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once("common.php");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die("資料庫連接失敗: " . $oDB->error());
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>訊息邏輯測試</title>
    <style>
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            white-space: pre-line;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>訊息邏輯測試</h1>
        
        <?php
        // 測試案例1：模擬全部成功
        echo "<div class='test-case'>";
        echo "<h3>測試案例1：全部成功</h3>";
        $result1 = [
            'success' => true,
            'message_type' => 'success',
            'message' => '轉換完成！成功：3 個XML檔案',
            'xml_files' => [
                ['filename' => 'US16_ACME_20241201_A_01.xml'],
                ['filename' => 'JP07_TOKYO_CHEM-A_20241201_A_01.xml'],
                ['filename' => 'SG08_1039151_M001_LOT001_CYL001.xml']
            ]
        ];
        
        $message1 = $result1['message'];
        $messageType1 = $result1['message_type'];
        if (!empty($result1['xml_files'])) {
            $message1 .= "\n\n生成的XML檔案：\n";
            foreach ($result1['xml_files'] as $xmlFile) {
                $message1 .= "- " . $xmlFile['filename'] . "\n";
            }
        }
        echo "<div class='message {$messageType1}'>{$message1}</div>";
        echo "</div>";
        
        // 測試案例2：模擬部分成功
        echo "<div class='test-case'>";
        echo "<h3>測試案例2：部分成功部分失敗</h3>";
        $result2 = [
            'success' => true,
            'message_type' => 'warning',
            'message' => '轉換部分完成！成功：2 個XML檔案，失敗：1 個',
            'xml_files' => [
                ['filename' => 'US16_ACME_20241201_A_01.xml'],
                ['filename' => 'JP07_TOKYO_CHEM-A_20241201_A_01.xml']
            ]
        ];
        
        $message2 = $result2['message'];
        $messageType2 = $result2['message_type'];
        $message2 .= "\n錯誤詳情：\n第 3 行：檢驗項目 'H2O' 的值 2.0 超過上限值 1.0";
        if (!empty($result2['xml_files'])) {
            $message2 .= "\n\n生成的XML檔案：\n";
            foreach ($result2['xml_files'] as $xmlFile) {
                $message2 .= "- " . $xmlFile['filename'] . "\n";
            }
        }
        echo "<div class='message {$messageType2}'>{$message2}</div>";
        echo "</div>";
        
        // 測試案例3：模擬全部失敗
        echo "<div class='test-case'>";
        echo "<h3>測試案例3：全部失敗</h3>";
        $result3 = [
            'success' => false,
            'message_type' => 'error',
            'message' => '轉換失敗！失敗：3 個XML檔案',
            'xml_files' => []
        ];
        
        $message3 = $result3['message'];
        $messageType3 = $result3['message_type'];
        $message3 .= "\n錯誤詳情：\n第 1 行：沒有找到對應的檢驗項目設定 MaterialName: 'Unknown Gas'\n第 2 行：檢驗項目 'H2O' 的值 2.0 超過上限值 1.0\n第 3 行：檢驗項目 'CO2' 的值 1.5 超過上限值 0.8";
        echo "<div class='message {$messageType3}'>{$message3}</div>";
        echo "</div>";
        ?>
        
        <h2>測試說明</h2>
        <ul>
            <li><strong>綠色背景 (success)</strong>：全部成功轉換，successCount > 0 && failedCount == 0</li>
            <li><strong>黃色背景 (warning)</strong>：部分成功部分失敗，successCount > 0 && failedCount > 0</li>
            <li><strong>紅色背景 (error)</strong>：全部失敗，successCount == 0 && failedCount > 0</li>
        </ul>
        
        <p><a href="ConvertImport.php">返回匯入頁面</a></p>
    </div>
</body>
</html>
