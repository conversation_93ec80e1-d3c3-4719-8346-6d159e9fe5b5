<?php
// 測試地區邏輯
ini_set('display_errors', 'on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once("common.php");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die("資料庫連接失敗: " . $oDB->error());
}

echo "<h1>地區邏輯測試</h1>";

// 測試地區前綴函數
$testUsers = [
    ['userId' => 'test_us', 'region' => 'US'],
    ['userId' => 'test_jp', 'region' => 'JP'],
    ['userId' => 'test_sg', 'region' => 'SG']
];

// 建立測試使用者
foreach ($testUsers as $user) {
    $checkSql = "SELECT COUNT(*) as count FROM ACCOUNT WHERE ACCOUNT_ID = :userId";
    $result = $oDB->execute($checkSql, [':userId' => $user['userId']]);
    
    if ($result && $oDB->fetchRow() && $oDB->rowArray['count'] == 0) {
        $insertSql = "INSERT INTO ACCOUNT (ACCOUNT_ID, NAME, REGION) VALUES (:userId, :name, :region)";
        $oDB->execute($insertSql, [
            ':userId' => $user['userId'],
            ':name' => 'Test User ' . $user['region'],
            ':region' => $user['region']
        ]);
        echo "<p>建立測試使用者: {$user['userId']} ({$user['region']})</p>";
    }
}

echo "<h2>地區前綴測試</h2>";
foreach ($testUsers as $user) {
    $regionInfo = getRegionPrefixes($oDB, $user['userId']);
    echo "<p><strong>{$user['userId']} ({$user['region']}):</strong></p>";
    echo "<ul>";
    echo "<li>前綴: " . implode(', ', $regionInfo['prefixes']) . "</li>";
    echo "<li>模式: {$regionInfo['mode']}</li>";
    echo "</ul>";
}

echo "<h2>檔名生成測試</h2>";

// 測試檔名生成
$testRowData = [
    'materialNumber' => 'TEST001',
    'cylinderNumber' => 'CYL001',
    'data' => ['TEST001', 'AGENCY_TEST', '********', 'CYL001', '0.5', '0.3', '0.2', '99.5']
];

$testHeaders = ['MaterialNumber', 'Agency', 'FabInDate', 'Cylinder Number', 'H2O', 'CO2', 'O2', 'PURITY'];

$prefixes = ['US16', 'JP07', 'SG08'];
foreach ($prefixes as $prefix) {
    $filename = generateFilenameByRegion($prefix, $testRowData, $testHeaders);
    echo "<p><strong>{$prefix}:</strong> " . ($filename ? $filename : "❌ 無法生成檔名") . "</p>";
}

echo "<p><a href='ConvertList.php'>返回主頁面</a></p>";
?>
