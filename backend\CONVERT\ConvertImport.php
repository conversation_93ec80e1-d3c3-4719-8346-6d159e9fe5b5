<?php
//ini_set('display_errors','on');
ini_set('display_errors','off');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once("common.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
  die(CONNECTION_DB_FAILED.$oDB->error());
}

$message = '';
$messageType = '';

// 處理檔案上傳
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['import_file'])) {
    // 取得當前登入的使用者ID
    global $loginAccount;
    $userId = $loginAccount->getAccountID() ?? 'admin';

    // 直接傳遞資料庫連接，讓convertCSVToXML函數自動根據MaterialName找到對應設定
    $result = convertCSVToXML($oDB, $_FILES['import_file'], $userId);
    $message = $result['message'];
    $messageType = $result['success'] ? 'success' : 'error';

    // 如果轉換成功，顯示生成的XML檔案列表
    if ($result['success'] && !empty($result['xml_files'])) {
        $message .= "\n\n生成的XML檔案：\n";
        foreach ($result['xml_files'] as $xmlFile) {
            $message .= "- " . $xmlFile['filename'] . "\n";
        }
    }
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-檔案匯入</title>
<style type="text/css">
  body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-image: url();
  }
  .style1 {
    font-size: 12px;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-weight: normal;
    color: #000000;
  }
  .import-container {
    background: #f9f9f9;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .import-form {
    max-width: 500px;
  }
  .form-group {
    margin-bottom: 15px;
  }
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  .form-group input[type="file"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    margin-right: 10px;
  }
  .btn-primary {
    background: #4CAF50;
    color: white;
  }
  .btn-primary:hover {
    background: #45a049;
  }
  .btn-secondary {
    background: #6c757d;
    color: white;
  }
  .btn-secondary:hover {
    background: #5a6268;
  }
  .btn-info {
    background: #17a2b8;
    color: white;
  }
  .btn-info:hover {
    background: #138496;
  }
  .btn-success {
    background: #28a745;
    color: white;
  }
  .btn-success:hover {
    background: #218838;
  }
  .btn-danger {
    background: #dc3545;
    color: white;
  }
  .btn-danger:hover {
    background: #c82333;
  }
  .btn-sm {
    padding: 5px 10px;
    font-size: 12px;
  }
  .message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
  }
  .message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  .message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  .instructions {
    background: #e7f3ff;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #007bff;
  }
  .instructions h4 {
    margin-top: 0;
    color: #007bff;
  }
  .instructions ul {
    margin-bottom: 0;
  }

  /* Modal樣式 */
  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
  }

  .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  .modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 18px;
  }

  .close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
  }

  .close:hover {
    opacity: 0.7;
  }

  .modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
    border-radius: 0 0 8px 8px;
  }

  .settings-container {
    width: 100%;
  }

  .settings-header {
    margin-bottom: 15px;
    text-align: right;
  }

  .settings-table-container {
    overflow-x: auto;
  }

  .settings-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }

  .settings-table th,
  .settings-table td {
    border: 1px solid #dee2e6;
    padding: 8px;
    text-align: left;
  }

  .settings-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
  }

  .settings-table .form-control {
    width: 90%;
    padding: 5px 8px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    font-size: 13px;
  }

  .settings-table .form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  }

  /* 轉檔類型分頁樣式 */
  .type-tabs {
    margin-bottom: 20px;
  }

  .tab-buttons {
    display: flex;
    border-bottom: 2px solid #dee2e6;
    gap: 5px;
  }

  .type-tab {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s ease;
  }

  .type-tab:hover {
    background: #e9ecef;
    color: #007bff;
  }

  .type-tab.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
    font-weight: bold;
  }

  /* 新增轉檔類型表單樣式 */
  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .header-left, .header-right {
    display: flex;
    gap: 10px;
  }

  .add-type-form {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
  }

  .add-type-form .form-group {
    margin-bottom: 15px;
  }

  .add-type-form label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #495057;
  }

  .form-buttons {
    display: flex;
    gap: 10px;
  }
</style>
<script type="text/javascript">
  function MM_preloadImages() { //v3.0
    var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
  }
  function MM_swapImgRestore() { //v3.0
    var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
  }
  function MM_findObj(n, d) { //v4.01
    var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
    if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
    for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
    if(!x && d.getElementById) x=d.getElementById(n); return x;
  }
  function MM_swapImage() { //v3.0
    var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
    if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
  }
  
  function validateForm() {
    var fileInput = document.getElementById('import_file');
    if (!fileInput.files.length) {
      alert('請選擇要匯入的檔案');
      return false;
    }

    var fileName = fileInput.files[0].name;
    var fileExtension = fileName.split('.').pop().toLowerCase();
    if (fileExtension !== 'csv') {
      alert('只支援CSV格式檔案');
      return false;
    }

    // 不再需要傳遞轉檔類型ID，系統會自動根據CSV中的MaterialNumber找到對應設定

    return confirm('確定要匯入此檔案嗎？');
  }
  
  function goBack() {
    window.location.href = 'ConvertList.php';
  }

  // 全域變數
  var currentTypeId = null;

  // 頁面載入時初始化
  document.addEventListener('DOMContentLoaded', function() {
    // 載入預設的轉檔類型（如果有的話）
    fetch('ConvertSettingsAPI.php?action=getTypes')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.types.length > 0) {
          currentTypeId = data.types[0].id; // 設定第一個類型為預設
        }
      })
      .catch(error => {
        console.error('載入轉檔類型失敗:', error);
      });
  });

  // Modal功能
  function openSettingsModal() {
    document.getElementById('settingsModal').style.display = 'block';
    loadInspectionTypes();
  }

  function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
  }

  // 點擊modal外部關閉
  window.onclick = function(event) {
    var modal = document.getElementById('settingsModal');
    if (event.target == modal) {
      closeSettingsModal();
    }
  }

  // 載入轉檔類型
  function loadInspectionTypes() {
    console.log('開始載入轉檔類型...');

    fetch('ConvertSettingsAPI.php?action=getTypes')
      .then(response => {
        console.log('API回應狀態:', response.status);
        return response.text();
      })
      .then(text => {
        console.log('API回應內容:', text);

        try {
          const data = JSON.parse(text);
          console.log('解析後的資料:', data);

          if (data.success) {
            var tabButtons = document.getElementById('typeTabButtons');
            tabButtons.innerHTML = '';

            if (data.types.length === 0) {
              tabButtons.innerHTML = '<p style="color: red;">沒有找到轉檔類型，請先執行資料庫安裝</p>';
              return;
            }

            data.types.forEach(function(type, index) {
              var tab = document.createElement('div');
              tab.className = 'type-tab' + (index === 0 ? ' active' : '');
              tab.textContent = type.type_name;
              tab.onclick = function() {
                switchTypeTab(type.id, this);
              };
              tabButtons.appendChild(tab);

              if (index === 0) {
                currentTypeId = type.id;
                loadCurrentSettings();
              }
            });
          } else {
            console.error('API錯誤:', data.message);
            document.getElementById('typeTabButtons').innerHTML = '<p style="color: red;">載入失敗: ' + data.message + '</p>';
          }
        } catch (e) {
          console.error('JSON解析錯誤:', e);
          document.getElementById('typeTabButtons').innerHTML = '<p style="color: red;">資料解析錯誤，請檢查API回應</p>';
        }
      })
      .catch(error => {
        console.error('載入轉檔類型失敗:', error);
        document.getElementById('typeTabButtons').innerHTML = '<p style="color: red;">網路錯誤，請重試</p>';
      });
  }

  // 切換轉檔類型分頁
  function switchTypeTab(typeId, tabElement) {
    // 更新分頁樣式
    document.querySelectorAll('.type-tab').forEach(tab => tab.classList.remove('active'));
    tabElement.classList.add('active');

    // 更新當前類型ID並載入設定
    currentTypeId = typeId;
    loadCurrentSettings();
  }

  // 顯示新增轉檔類型表單
  function showAddTypeForm() {
    document.getElementById('addTypeForm').style.display = 'block';
    document.getElementById('newMaterialNumber').focus();
  }

  // 隱藏新增轉檔類型表單
  function hideAddTypeForm() {
    document.getElementById('addTypeForm').style.display = 'none';
    document.getElementById('newMaterialNumber').value = '';
  }

  // 儲存新的轉檔類型
  function saveNewType() {
    var materialNumber = document.getElementById('newMaterialNumber').value.trim();

    if (!materialNumber) {
      alert('請輸入Material Number');
      return;
    }

    // 發送到後端儲存
    fetch('ConvertSettingsAPI.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'addType',
        materialNumber: materialNumber
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('轉檔類型已新增！');
        hideAddTypeForm();
        loadInspectionTypes(); // 重新載入分頁
      } else {
        alert('新增失敗：' + data.message);
      }
    })
    .catch(error => {
      console.error('新增轉檔類型失敗:', error);
      alert('新增轉檔類型失敗，請重試');
    });
  }

  // 新增檢驗項目
  function addInspectionItem() {
    if (!currentTypeId) {
      alert('請先選擇轉檔類型');
      return;
    }

    var tbody = document.getElementById('inspectionTableBody');

    // 如果表格中只有提示訊息，先清除它
    var existingRows = tbody.querySelectorAll('tr');
    if (existingRows.length === 1) {
      var firstRow = existingRows[0];
      var inputs = firstRow.querySelectorAll('input');
      if (inputs.length === 0) {
        // 這是提示訊息行，清除它
        tbody.innerHTML = '';
      }
    }

    var newRow = document.createElement('tr');
    newRow.innerHTML = `
      <td><input type="text" value="" class="form-control" placeholder="項目名稱"></td>
      <td><input type="text" value="" class="form-control" placeholder="規格"></td>
      <td><input type="number" value="" step="0.1" class="form-control" placeholder="檢測限"></td>
      <td><input type="number" value="" step="0.1" class="form-control" placeholder="上限值"></td>
      <td><button type="button" class="btn btn-danger btn-sm" onclick="removeInspectionItem(this)">刪除</button></td>
    `;
    tbody.appendChild(newRow);

    // 聚焦到第一個輸入框
    var firstInput = newRow.querySelector('input');
    if (firstInput) {
      firstInput.focus();
    }
  }

  // 刪除檢驗項目
  function removeInspectionItem(button) {
    if (confirm('確定要刪除此項目嗎？')) {
      button.closest('tr').remove();
    }
  }

  // 儲存設定到資料庫
  function saveSettings() {
    if (!currentTypeId) {
      alert('請先選擇轉檔類型');
      return;
    }

    var settings = [];
    var rows = document.querySelectorAll('#inspectionTableBody tr');

    console.log('找到', rows.length, '行資料');

    rows.forEach(function(row, index) {
      var inputs = row.querySelectorAll('input');
      console.log('第', index + 1, '行有', inputs.length, '個input元素');

      // 檢查是否有足夠的input元素，並且第一個input有值
      if (inputs.length >= 4 && inputs[0] && inputs[0].value && inputs[0].value.trim()) {
        var setting = {
          itemName: inputs[0].value.trim(),
          specification: inputs[1].value.trim(),
          detectionLimit: parseFloat(inputs[2].value) || 0,
          ucl: parseFloat(inputs[3].value) || 0
        };
        settings.push(setting);
        console.log('新增設定:', setting);
      } else {
        console.log('第', index + 1, '行跳過（沒有足夠的input或第一個input為空）');
      }
    });

    console.log('總共收集到', settings.length, '個設定項目');

    if (settings.length === 0) {
      alert('請至少新增一個檢驗項目');
      return;
    }

    // 發送到後端儲存
    console.log('發送資料到API:', {
      action: 'saveSettings',
      typeId: currentTypeId,
      settings: settings
    });

    fetch('ConvertSettingsAPI.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'saveSettings',
        typeId: currentTypeId,
        settings: settings
      })
    })
    .then(response => {
      console.log('API回應狀態:', response.status);
      return response.text();
    })
    .then(text => {
      console.log('API回應內容:', text);
      try {
        const data = JSON.parse(text);
        if (data.success) {
          alert('設定已儲存！');
          closeSettingsModal();
        } else {
          alert('儲存失敗：' + data.message);
        }
      } catch (e) {
        console.error('JSON解析錯誤:', e);
        alert('伺服器回應格式錯誤，請檢查伺服器日誌');
      }
    })
    .catch(error => {
      console.error('儲存設定失敗:', error);
      alert('儲存設定失敗，請重試');
    });
  }

  // 載入當前類型的設定
  function loadCurrentSettings() {
    if (!currentTypeId) {
      console.log('沒有選擇轉檔類型');
      return;
    }

    console.log('載入類型ID:', currentTypeId, '的設定');

    fetch('ConvertSettingsAPI.php?action=getSettings&typeId=' + currentTypeId)
      .then(response => response.text())
      .then(text => {
        console.log('設定API回應:', text);

        try {
          const data = JSON.parse(text);
          if (data.success) {
            var tbody = document.getElementById('inspectionTableBody');
            tbody.innerHTML = ''; // 清空現有內容

            if (data.settings.length === 0) {
              tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #666;">此類型尚未設定檢驗項目，請點擊「新增項目」開始設定</td></tr>';
            } else {
              data.settings.forEach(function(item) {
                var newRow = document.createElement('tr');
                newRow.innerHTML = `
                  <td><input type="text" value="${item.item_name}" class="form-control"></td>
                  <td><input type="text" value="${item.specification || ''}" class="form-control"></td>
                  <td><input type="number" value="${item.detection_limit || ''}" step="0.1" class="form-control"></td>
                  <td><input type="number" value="${item.ucl || ''}" step="0.1" class="form-control"></td>
                  <td><button type="button" class="btn btn-danger btn-sm" onclick="removeInspectionItem(this)">刪除</button></td>
                `;
                tbody.appendChild(newRow);
              });
            }
          } else {
            console.error('載入設定失敗:', data.message);
          }
        } catch (e) {
          console.error('設定資料解析錯誤:', e);
        }
      })
      .catch(error => {
        console.error('載入設定失敗:', error);
      });
  }
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
  <table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
    <tr>
      <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
    </tr>
    <tr>
      <th width="150" valign="top" bgcolor="#FFFFFF" scope="row">
        <table width="140" border="0" cellspacing="0" cellpadding="0">
    <?php include ("../left.php"); ?>
        </table>
      </th>
      <td width="810" valign="top" bgcolor="#FFFFFF">
        <table width="810" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <th height="50" align="left" scope="row"><span class="style1">【檔案匯入】</span></th>
          </tr>
          <tr>
            <th align="left" valign="top" scope="row">
              
              <?php if (!empty($message)): ?>
              <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
              </div>
              <?php endif; ?>
              
              <div class="import-container">
                <form class="import-form" method="post" enctype="multipart/form-data" onsubmit="return validateForm()">
                  <div class="form-group">
                    <label for="import_file">選擇CSV檔案：</label>
                    <input type="file" id="import_file" name="import_file" accept=".csv" />
                  </div>
                  
                  <div class="form-group">
                    <button type="button" class="btn btn-info" onclick="openSettingsModal()">⚙️ 檢驗項目設定</button>
                    <button type="submit" class="btn btn-primary">匯入檔案</button>
                    <button type="button" class="btn btn-secondary" onclick="goBack()">返回列表</button>
                  </div>
                </form>
              </div>
              
              <?php
              // 取得當前使用者的地區資訊
              global $loginAccount;
              $currentUserId = $loginAccount->getAccountID();
              $currentUserName = $loginAccount->getName();

              // 查詢使用者的地區設定
              $userRegion = 'SG'; // 預設值
              $regionName = '新加坡';
              $prefixes = ['SG08', 'SG18', 'SG28'];

              try {
                  $sql = "SELECT REGION FROM ACCOUNT WHERE ACCOUNT_ID = :userId";
                  $result = $oDB->execute($sql, [':userId' => $currentUserId]);

                  if ($result && $oDB->fetchRow()) {
                      $userRegion = $oDB->rowArray['REGION'] ?? 'SG';

                      switch ($userRegion) {
                          case 'JP':
                              $regionName = '日本';
                              $prefixes = ['JP07'];
                              break;
                          case 'US':
                              $regionName = '美國';
                              $prefixes = ['US16'];
                              break;
                          case 'SG':
                          default:
                              $regionName = '新加坡';
                              $prefixes = ['SG08', 'SG18', 'SG28'];
                              break;
                      }
                  }
              } catch (Exception $e) {
                  // 使用預設值
              }
              ?>

              <div class="instructions">
                <h4>當前地區設定：</h4>
                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-bottom: 15px;">
                  <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                    <div><strong>使用者：</strong> <?php echo htmlspecialchars($currentUserName); ?> (<?php echo htmlspecialchars($currentUserId); ?>)</div>
                    <div><strong>地區：</strong> <?php echo $regionName; ?> (<?php echo $userRegion; ?>)</div>
                  </div>
                  <div style="margin-top: 10px;">
                    <strong>檔名前綴：</strong>
                    <?php foreach ($prefixes as $prefix): ?>
                      <span style="background: #007bff; color: white; padding: 3px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;"><?php echo $prefix; ?></span>
                    <?php endforeach; ?>
                  </div>
                  <div style="margin-top: 10px; color: #6c757d; font-size: 13px;">
                    <strong>檔名格式：</strong> {前綴}_1039151_{MaterialNumber}_{LotNumber}_{CylinderNumber}.xml
                  </div>
                </div>

                <h4>CSV轉XML說明：</h4>
                <ul>
                  <li>請上傳CSV格式的檔案</li>
                  <li>系統會根據您的地區設定自動產生對應的檔名前綴</li>
                  <li>每一行資料將生成3個XML檔案（對應不同前綴）</li>
                  <li>XML檔案將儲存在伺服器上，可供下載</li>
                  <li>檔案大小限制：10MB</li>
                  <li>支援的編碼：UTF-8</li>
                </ul>
              </div>

            </th>
          </tr>
        </table>
      </td>
    </tr>
  </table>

  <!-- 檢驗項目設定Modal -->
  <div id="settingsModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>檢驗項目設定</h3>
        <span class="close" onclick="closeSettingsModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="settings-container">
          <!-- 轉檔類型分頁 -->
          <div class="type-tabs">
            <div class="tab-buttons" id="typeTabButtons">
              <!-- 動態載入轉檔類型標籤 -->
            </div>
          </div>

          <div class="settings-header">
            <div class="header-left">
              <button type="button" class="btn btn-primary btn-sm" onclick="showAddTypeForm()">+ 新增轉檔類型</button>
            </div>
            <div class="header-right">
              <button type="button" class="btn btn-success btn-sm" onclick="addInspectionItem()">+ 新增項目</button>
              <button type="button" class="btn btn-info btn-sm" onclick="loadCurrentSettings()">🔄 重新載入</button>
            </div>
          </div>

          <!-- 新增轉檔類型表單 -->
          <div id="addTypeForm" class="add-type-form" style="display: none;">
            <div class="form-group">
              <label>Material Number：</label>
              <input type="text" id="newMaterialNumber" class="form-control" placeholder="請輸入Material Number">
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-primary btn-sm" onclick="saveNewType()">儲存</button>
              <button type="button" class="btn btn-secondary btn-sm" onclick="hideAddTypeForm()">取消</button>
            </div>
          </div>

          <div class="settings-table-container">
            <table class="settings-table" id="inspectionTable">
              <thead>
                <tr>
                  <th>Inspection Item</th>
                  <th>Specification</th>
                  <th>Detection Limit</th>
                  <th>UCL</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="inspectionTableBody">
                <!-- 動態載入檢驗項目 -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" onclick="saveSettings()">儲存設定</button>
        <button type="button" class="btn btn-secondary" onclick="closeSettingsModal()">取消</button>
      </div>
    </div>
  </div>

</body>
</html>
