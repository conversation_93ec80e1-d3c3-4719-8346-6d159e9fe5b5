<?php
// 測試轉檔功能
ini_set('display_errors', 'on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once("common.php");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die("資料庫連接失敗: " . $oDB->error());
}

echo "<h1>轉檔功能測試</h1>";

// 測試資料
$testCases = [
    [
        'name' => '美國測試資料',
        'file' => 'test_data/test_us_data.csv',
        'userId' => 'test_us_user',
        'region' => 'US',
        'expectedFilename' => 'US16_ACME_20241201_A_01.xml'
    ],
    [
        'name' => '日本測試資料',
        'file' => 'test_data/test_jp_data.csv',
        'userId' => 'test_jp_user',
        'region' => 'JP',
        'expectedFilename' => 'JP07_TOKYO_CHEM-A_20241201_A_01.xml'
    ],
    [
        'name' => '新加坡測試資料',
        'file' => 'test_data/test_sg_data.csv',
        'userId' => 'test_sg_user',
        'region' => 'SG',
        'expectedFilename' => 'SG08_1039151_M001_SG_LOT001_SG001.xml'
    ]
];

// 建立測試使用者
foreach ($testCases as $testCase) {
    // 檢查使用者是否存在，不存在則建立
    $checkUserSql = "SELECT COUNT(*) as count FROM ACCOUNT WHERE ACCOUNT_ID = :userId";
    $result = $oDB->execute($checkUserSql, [':userId' => $testCase['userId']]);
    
    if ($result && $oDB->fetchRow() && $oDB->rowArray['count'] == 0) {
        $insertUserSql = "INSERT INTO ACCOUNT (ACCOUNT_ID, NAME, REGION) VALUES (:userId, :name, :region)";
        $oDB->execute($insertUserSql, [
            ':userId' => $testCase['userId'],
            ':name' => $testCase['name'],
            ':region' => $testCase['region']
        ]);
        echo "<p>建立測試使用者: {$testCase['userId']} ({$testCase['region']})</p>";
    }
}

// 執行測試
foreach ($testCases as $index => $testCase) {
    echo "<h2>測試 " . ($index + 1) . ": {$testCase['name']}</h2>";
    
    $filePath = __DIR__ . '/' . $testCase['file'];
    if (!file_exists($filePath)) {
        echo "<p style='color: red;'>❌ 測試檔案不存在: {$filePath}</p>";
        continue;
    }
    
    // 模擬上傳檔案
    $fileInfo = [
        'name' => basename($testCase['file']),
        'tmp_name' => $filePath,
        'size' => filesize($filePath),
        'type' => 'text/csv'
    ];
    
    echo "<p>📁 測試檔案: {$testCase['file']}</p>";
    echo "<p>👤 測試使用者: {$testCase['userId']} (地區: {$testCase['region']})</p>";
    
    // 執行轉檔
    $result = convertCSVToXML($oDB, $fileInfo, $testCase['userId']);
    
    if ($result['success']) {
        echo "<p style='color: green;'>✅ 轉檔成功!</p>";
        echo "<p>📊 結果: {$result['message']}</p>";
        
        if (!empty($result['xml_files'])) {
            echo "<p><strong>生成的XML檔案:</strong></p>";
            echo "<ul>";
            foreach ($result['xml_files'] as $xmlFile) {
                echo "<li>{$xmlFile['filename']} (序號: {$xmlFile['seq']})</li>";
                
                // 檢查檔案是否存在
                if (file_exists($xmlFile['path'])) {
                    echo "<li style='margin-left: 20px; color: green;'>✅ 檔案已建立</li>";
                    
                    // 顯示XML內容的前幾行
                    $xmlContent = file_get_contents($xmlFile['path']);
                    $lines = explode("\n", $xmlContent);
                    echo "<li style='margin-left: 20px;'><strong>XML內容預覽:</strong></li>";
                    echo "<pre style='margin-left: 40px; background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
                    echo htmlspecialchars(implode("\n", array_slice($lines, 0, 20)));
                    if (count($lines) > 20) {
                        echo "\n... (省略 " . (count($lines) - 20) . " 行)";
                    }
                    echo "</pre>";
                } else {
                    echo "<li style='margin-left: 20px; color: red;'>❌ 檔案未建立</li>";
                }
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>❌ 轉檔失敗: {$result['message']}</p>";
    }
    
    echo "<hr>";
}

echo "<h2>測試完成</h2>";
echo "<p><a href='ConvertList.php'>返回主頁面</a></p>";
?>
